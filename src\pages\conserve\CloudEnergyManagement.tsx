import React, { useState, useEffect } from 'react';
import { ChevronDown, Factory, Brain, Shield, Zap, Target, Cpu, Globe, BarChart3, Settings, CheckCircle, ArrowRight, Menu, X } from 'lucide-react';

interface Feature {
  icon: React.ReactNode;
  title: string;
  description: string;
}

interface NavigationItem {
  name: string;
  href: string;
}

const SmartFactoryLanding: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = useState<boolean>(false);
  const [activeSection, setActiveSection] = useState<number>(0);

  useEffect(() => {
    const handleScroll = (): void => {
      const sections = document.querySelectorAll('section[id]');
      const scrollPosition = window.scrollY + 100;

      sections.forEach((section, index) => {
        const element = section as HTMLElement;
        const sectionTop = element.offsetTop;
        const sectionHeight = element.offsetHeight;
        
        if (scrollPosition >= sectionTop && scrollPosition < sectionTop + sectionHeight) {
          setActiveSection(index);
        }
      });
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const features: Feature[] = [
    {
      icon: <Brain className="w-8 h-8" />,
      title: "Intelligent Closed-Loop Operations",
      description: "Drive new generation systems powered by real-time digital data and intelligent orchestration for streamlined workflows."
    },
    {
      icon: <Shield className="w-8 h-8" />,
      title: "Predictive Edge-Based AIoT Platform",
      description: "Leverage intelligent AIoT design to analyze data, deliver predictive insights and enable early intervention."
    },
    {
      icon: <Target className="w-8 h-8" />,
      title: "Depth of Traceability & Genealogy",
      description: "Embedded Digital Twin technology offers high degree of spatial awareness across workflows with precise tracking."
    },
    {
      icon: <Globe className="w-8 h-8" />,
      title: "Distributed Location Production",
      description: "Drive production orchestration across multiple sites with unified view of operations globally."
    },
    {
      icon: <Zap className="w-8 h-8" />,
      title: "Automatic Anomaly Detection",
      description: "Catch production anomalies before they impact delivery schedules with intelligent alerts and contextual analysis."
    },
    {
      icon: <Settings className="w-8 h-8" />,
      title: "Digital Factory Synchronization",
      description: "Optimize digital workflows, reduce wait times and eliminate unnecessary delays automatically."
    }
  ];

  const benefits: string[] = [
    "Deliver productivity with optimal resources",
    "Enable quality in process and by design",
    "Balance cost with adaptability to changes",
    "Elevate engineering for accelerated innovation"
  ];

  const navigationItems: NavigationItem[] = [
    { name: 'Home', href: '#home' },
    { name: 'Features', href: '#features' },
    { name: 'Benefits', href: '#benefits' },
    { name: 'Digital Twin', href: '#digital-twin' },
    { name: 'Contact', href: '#contact' }
  ];

  const handleMenuToggle = (): void => {
    setIsMenuOpen(!isMenuOpen);
  };

  const handleMenuItemClick = (): void => {
    setIsMenuOpen(false);
  };

  const scrollToSection = (href: string): void => {
    const element = document.querySelector(href);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-green-900 to-slate-800 text-white">
      {/* Navigation */}
      <nav className="fixed top-0 w-full z-50 bg-black/20 backdrop-blur-md border-b border-green-500/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <Factory className="w-8 h-8 text-green-400" />
              <span className="text-xl font-bold bg-gradient-to-r from-green-400 to-emerald-300 bg-clip-text text-transparent">
                Atandra
              </span>
            </div>
            
            {/* Desktop Navigation */}
            <div className="hidden md:flex space-x-8">
              {navigationItems.map((item, index) => (
                <button
                  key={item.name}
                  onClick={() => scrollToSection(item.href)}
                  className={`px-3 py-2 rounded-md text-sm font-medium transition-all duration-300 hover:text-green-400 ${
                    activeSection === index ? 'text-green-400 border-b-2 border-green-400' : 'text-gray-300'
                  }`}
                >
                  {item.name}
                </button>
              ))}
            </div>

            {/* Mobile menu button */}
            <button
              className="md:hidden p-2 rounded-md text-gray-300 hover:text-green-400"
              onClick={handleMenuToggle}
              aria-label="Toggle menu"
            >
              {isMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden bg-black/90 backdrop-blur-md">
            <div className="px-2 pt-2 pb-3 space-y-1">
              {navigationItems.map((item) => (
                <button
                  key={item.name}
                  onClick={() => {
                    scrollToSection(item.href);
                    handleMenuItemClick();
                  }}
                  className="block w-full text-left px-3 py-2 rounded-md text-base font-medium text-gray-300 hover:text-green-400 hover:bg-green-500/10"
                >
                  {item.name}
                </button>
              ))}
            </div>
          </div>
        )}
      </nav>

      {/* Hero Section */}
      <section id="home" className="relative min-h-screen flex items-center justify-center overflow-hidden">
        <div className="absolute inset-0 opacity-20">
          <div className="w-full h-full bg-repeat" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23059669' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='4'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
          }} />
        </div>
        
        <div className="relative z-10 max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="mb-8">
            <div className="inline-flex items-center space-x-4 bg-green-500/10 backdrop-blur-sm border border-green-500/20 rounded-full px-6 py-3 mb-6">
              <Cpu className="w-6 h-6 text-green-400" />
              <span className="text-green-300 font-medium">Cantier MES X.0</span>
            </div>
            
            <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-6 leading-tight">
              <span className="bg-gradient-to-r from-green-400 via-emerald-300 to-green-500 bg-clip-text text-transparent">
                Intelligently ahead.
              </span>
              <br />
              <span className="text-white">Next Generation Manufacturing.</span>
            </h1>
            
            <p className="text-xl md:text-2xl text-gray-300 mb-8 max-w-4xl mx-auto leading-relaxed">
              Take a transformative leap in agile and resilient manufacturing ecosystems. Experience 
              intelligence-driven manufacturing automation with advanced digital MES technologies.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <button className="group bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 px-8 py-4 rounded-full font-semibold text-white shadow-lg hover:shadow-green-500/25 transition-all duration-300 transform hover:scale-105 flex items-center space-x-2">
                <span>Explore Solutions</span>
                <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
              </button>
              <button className="border-2 border-green-500 hover:bg-green-500/10 px-8 py-4 rounded-full font-semibold text-green-400 hover:text-green-300 transition-all duration-300">
                Watch Demo
              </button>
            </div>
          </div>
        </div>

        {/* Scroll Indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <ChevronDown className="w-8 h-8 text-green-400" />
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 bg-gradient-to-b from-transparent to-black/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              <span className="bg-gradient-to-r from-green-400 to-emerald-300 bg-clip-text text-transparent">
                Why choose Cantier MES X.0?
              </span>
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Globally, Cantier places the highest emphasis on MES implementation with intelligence 
              at the heart of your workflow operations.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div
                key={index}
                className="group bg-gradient-to-br from-green-500/10 to-emerald-500/5 backdrop-blur-sm border border-green-500/20 rounded-2xl p-6 hover:border-green-400/40 transition-all duration-300 hover:transform hover:scale-105 hover:shadow-xl hover:shadow-green-500/10"
              >
                <div className="flex items-center justify-center w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl mb-4 group-hover:from-green-400 group-hover:to-emerald-500 transition-all duration-300">
                  {feature.icon}
                </div>
                <h3 className="text-xl font-bold mb-3 text-green-300 group-hover:text-green-200 transition-colors">
                  {feature.title}
                </h3>
                <p className="text-gray-300 leading-relaxed">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section id="benefits" className="py-20 bg-gradient-to-r from-green-900/20 to-emerald-900/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-4xl md:text-5xl font-bold mb-8">
                <span className="bg-gradient-to-r from-green-400 to-emerald-300 bg-clip-text text-transparent">
                  Benefits for your manufacturing business
                </span>
              </h2>
              
              <div className="space-y-6">
                {benefits.map((benefit, index) => (
                  <div key={index} className="flex items-start space-x-4 group">
                    <div className="flex-shrink-0">
                      <CheckCircle className="w-6 h-6 text-green-400 group-hover:text-green-300 transition-colors" />
                    </div>
                    <p className="text-lg text-gray-300 group-hover:text-white transition-colors">
                      {benefit}
                    </p>
                  </div>
                ))}
              </div>

              <div className="mt-8">
                <button className="group bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 px-8 py-4 rounded-full font-semibold text-white shadow-lg hover:shadow-green-500/25 transition-all duration-300 transform hover:scale-105 flex items-center space-x-2">
                  <span>Learn More</span>
                  <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
                </button>
              </div>
            </div>

            <div className="relative">
              <div className="bg-gradient-to-br from-green-500/20 to-emerald-500/10 backdrop-blur-sm border border-green-500/30 rounded-3xl p-8 shadow-2xl">
                <BarChart3 className="w-16 h-16 text-green-400 mb-6" />
                <h3 className="text-2xl font-bold text-green-300 mb-4">
                  Performance Enhancement
                </h3>
                <p className="text-gray-300 leading-relaxed">
                  Achieve flawless deliveries by maximizing resource utilization and maintaining 
                  precise control over production across the shopfloor with integrated quality 
                  management systems.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Digital Twin Section */}
      <section id="digital-twin" className="py-20 bg-gradient-to-b from-black/20 to-transparent">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              <span className="bg-gradient-to-r from-green-400 to-emerald-300 bg-clip-text text-transparent">
                Drive performance and innovation with Immersive Digital Twin
              </span>
            </h2>
            <p className="text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed">
              Replicate your physical shopfloor assets and systems in a digital environment. 
              Gain spatial awareness with immersive Digital Twin capabilities that enable 
              enhanced visualizations for monitoring, analyzing, and optimizing performance.
            </p>
          </div>

          <div className="bg-gradient-to-br from-green-500/10 to-emerald-500/5 backdrop-blur-sm border border-green-500/20 rounded-3xl p-8 md:p-12">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
              <div className="group">
                <div className="bg-gradient-to-br from-green-500 to-emerald-600 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                  <Cpu className="w-10 h-10 text-white" />
                </div>
                <h3 className="text-xl font-bold text-green-300 mb-2">Model Scenarios</h3>
                <p className="text-gray-300">Simulate changes and predict outcomes</p>
              </div>
              
              <div className="group">
                <div className="bg-gradient-to-br from-green-500 to-emerald-600 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                  <Settings className="w-10 h-10 text-white" />
                </div>
                <h3 className="text-xl font-bold text-green-300 mb-2">Optimize Performance</h3>
                <p className="text-gray-300">Enhance digital visualizations</p>
              </div>
              
              <div className="group">
                <div className="bg-gradient-to-br from-green-500 to-emerald-600 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                  <BarChart3 className="w-10 h-10 text-white" />
                </div>
                <h3 className="text-xl font-bold text-green-300 mb-2">Proactive Actions</h3>
                <p className="text-gray-300">Drive smarter factory performance</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section id="contact" className="py-20 bg-gradient-to-t from-green-900/20 to-transparent">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              <span className="bg-gradient-to-r from-green-400 to-emerald-300 bg-clip-text text-transparent">
                Elevate your performance horizons
              </span>
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Harness AI-powered, next-generation Manufacturing Execution Systems from an industry leader.
            </p>
          </div>

          <div className="bg-gradient-to-br from-green-500/10 to-emerald-500/5 backdrop-blur-sm border border-green-500/20 rounded-3xl p-8 md:p-12 text-center">
            <div className="mb-8">
              <h3 className="text-2xl font-bold text-green-300 mb-4">
                Authorized Partner for Cantier Systems Pte. Ltd.
              </h3>
              <div className="text-lg text-gray-300 space-y-2">
                <p className="font-semibold text-white">Atandra Energy Private Limited</p>
                <p>No. 5, Kumaran St., Pazhavanthangal, Chennai - 600 114</p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mt-6">
                  <a href="mailto:<EMAIL>" className="text-green-400 hover:text-green-300 transition-colors">
                    <EMAIL>
                  </a>
                  <a href="https://atandra.in" className="text-green-400 hover:text-green-300 transition-colors" target="_blank" rel="noopener noreferrer">
                    atandra.in
                  </a>
                  <a href="tel:+919500097966" className="text-green-400 hover:text-green-300 transition-colors">
                    +91 95000 97966
                  </a>
                </div>
              </div>
            </div>

            <button className="group bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 px-12 py-4 rounded-full font-semibold text-white shadow-lg hover:shadow-green-500/25 transition-all duration-300 transform hover:scale-105 flex items-center space-x-2 mx-auto">
              <span>Get Started Today</span>
              <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
            </button>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="border-t border-green-500/20 py-8 bg-black/30">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex items-center space-x-4 mb-4 md:mb-0">
              <Factory className="w-8 h-8 text-green-400" />
              <span className="text-xl font-bold bg-gradient-to-r from-green-400 to-emerald-300 bg-clip-text text-transparent">
                Atandra
              </span>
            </div>
            <div className="text-gray-400 text-sm text-center md:text-right">
              <p>&copy; 2025 Atandra Energy Private Limited. All rights reserved.</p>
              <p className="mt-1">ISO 9001:2015 | 14001 - 2015 | 45001 - 2015 | 50001</p>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default SmartFactoryLanding;