import React, { useState } from 'react';
import { ChevronDown, Zap, TrendingUp, Shield, Award, Users, Monitor, BarChart3, Leaf, Settings, CheckCircle, ArrowRight, Menu, X, Home, ChevronRight, Calendar, Download, Phone, Mail } from 'lucide-react';

const AlensoftEnMSProductPage = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');
  const [selectedModule, setSelectedModule] = useState(null);

  const stats = [
    { label: 'Typical Energy Savings', value: '8-15%', icon: TrendingUp, color: 'from-green-400 to-green-600' },
    { label: 'Payback Period', value: '1.5-2 Years', icon: Award, color: 'from-emerald-400 to-emerald-600' },
    { label: 'Customers Served', value: '300+', icon: Users, color: 'from-teal-400 to-teal-600' },
    { label: 'Devices Connected', value: '20,000+', icon: Monitor, color: 'from-green-500 to-green-700' }
  ];

  const addOnModules = [
    {
      title: 'Power Quality Management',
      description: 'Integrate with PQ meters & monitor electrical system performance',
      icon: Zap,
      features: ['Real-time power quality monitoring', 'Harmonic analysis', 'Voltage fluctuation tracking', 'Equipment protection alerts']
    },
    {
      title: 'Advanced Demand Management',
      description: 'Optimize energy use during peak demand periods',
      icon: BarChart3,
      features: ['Peak demand forecasting', 'Load shifting strategies', 'Cost optimization', 'Automated load control']
    },
    {
      title: 'Utility Asset Performance',
      description: 'Maximize the performance & conserve Energy of critical Utility Assets',
      icon: Settings,
      features: ['Asset health monitoring', 'Performance optimization', 'Energy conservation tracking', 'Maintenance scheduling']
    },
    {
      title: 'ISO 50001 Compliance',
      description: 'Meet the requirements of international energy management standards',
      icon: Shield,
      features: ['Compliance documentation', 'Audit trail management', 'Standard reporting', 'Certification support']
    },
    {
      title: 'GHG & Sustainability Reporting',
      description: 'Comprehensive carbon footprint management & Compliance reporting',
      icon: Leaf,
      features: ['Carbon footprint calculation', 'Emission tracking', 'Sustainability metrics', 'Regulatory compliance']
    },
    {
      title: 'Asset Management',
      description: 'Predictive & Planned maintenance of Assets',
      icon: Monitor,
      features: ['Predictive maintenance', 'Asset lifecycle tracking', 'Maintenance scheduling', 'Performance analytics']
    }
  ];

  const coreFeatures = [
    'Real-time energy monitoring and analytics',
    'AI-powered predictive intelligence',
    'Automated energy optimization',
    'Comprehensive reporting dashboard',
    'Multi-site management capabilities',
    'Mobile and web-based access',
    'Integration with existing systems',
    'Custom alert and notification system'
  ];

  const benefits = [
    { title: 'Cost Reduction', description: '8-15% reduction in energy costs through intelligent optimization', icon: TrendingUp },
    { title: 'Quick ROI', description: 'Payback period of just 1.5-2 years with immediate savings', icon: Award },
    { title: 'Sustainability', description: 'Reduce carbon footprint and meet environmental goals', icon: Leaf },
    { title: 'Compliance', description: 'Meet international energy management standards', icon: Shield }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-green-50">
      {/* Navigation Breadcrumb */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3">
          <nav className="flex items-center space-x-2 text-sm text-gray-600">
            <Home className="w-4 h-4" />
            <ChevronRight className="w-4 h-4" />
            <span>Products</span>
            <ChevronRight className="w-4 h-4" />
            <span>Energy Management</span>
            <ChevronRight className="w-4 h-4" />
            <span className="text-green-600 font-medium">Smart EnMS</span>
          </nav>
        </div>
      </div>

      {/* Header Section */}
      <div className="relative bg-gradient-to-r from-green-600 via-green-700 to-emerald-800 text-white overflow-hidden">
        <div className="absolute inset-0 bg-black opacity-20"></div>
        <div className="absolute inset-0 bg-gradient-to-r from-green-600/90 to-transparent"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 lg:py-24">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-8">
              <div className="space-y-4">
                <div className="inline-flex items-center px-3 py-1 rounded-full bg-green-500/20 border border-green-400/30">
                  <Zap className="w-4 h-4 mr-2" />
                  <span className="text-sm font-medium">AI-Powered Energy Management</span>
                </div>
                <h1 className="text-4xl lg:text-5xl font-bold leading-tight">
                  SMART ENERGY MANAGEMENT SYSTEM
                  <span className="block text-green-300 text-2xl lg:text-3xl mt-2">(EnMS)</span>
                </h1>
                <p className="text-xl text-green-100 leading-relaxed">
                  Predictive & proactive intelligence with built-in AI & analytics for energy savings right out of the box
                </p>
              </div>
              
              <div className="flex flex-wrap gap-4">
                <button className="bg-white text-green-700 px-6 py-3 rounded-lg font-semibold hover:bg-green-50 transition-colors flex items-center">
                  <Download className="w-5 h-5 mr-2" />
                  Download Brochure
                </button>
                <button className="border-2 border-white text-white px-6 py-3 rounded-lg font-semibold hover:bg-white hover:text-green-700 transition-colors flex items-center">
                  <Calendar className="w-5 h-5 mr-2" />
                  Schedule Demo
                </button>
              </div>
            </div>
            
            <div className="relative">
              <img 
                src="https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=600&h=400&fit=crop&auto=format" 
                alt="Smart Energy Management Dashboard"
                className="rounded-xl shadow-2xl transform rotate-2 hover:rotate-0 transition-transform duration-500"
              />
              <div className="absolute -bottom-4 -left-4 bg-white p-4 rounded-lg shadow-lg">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                  <span className="text-sm font-medium text-gray-700">Live Monitoring</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Stats Section */}
      <div className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => {
              const IconComponent = stat.icon;
              return (
                <div key={index} className="text-center group">
                  <div className={`inline-flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-r ${stat.color} mb-4 group-hover:scale-110 transition-transform`}>
                    <IconComponent className="w-8 h-8 text-white" />
                  </div>
                  <div className="text-3xl font-bold text-gray-900 mb-2">{stat.value}</div>
                  <div className="text-gray-600 font-medium">{stat.label}</div>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Product Overview Tabs */}
      <div className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Comprehensive Energy Management Solution
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Transform how you monitor, manage, and optimize energy resources across your entire operations with our AI-powered platform.
            </p>
          </div>

          {/* Tab Navigation */}
          <div className="flex flex-wrap justify-center mb-12 bg-white rounded-xl p-2 shadow-lg max-w-2xl mx-auto">
            {[
              { id: 'overview', label: 'Overview' },
              { id: 'features', label: 'Core Features' },
              { id: 'benefits', label: 'Benefits' },
              { id: 'modules', label: 'Add-on Modules' }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`px-6 py-3 rounded-lg font-medium transition-all ${
                  activeTab === tab.id
                    ? 'bg-green-600 text-white shadow-md'
                    : 'text-gray-600 hover:text-green-600 hover:bg-green-50'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </div>

          {/* Tab Content */}
          <div className="bg-white rounded-xl shadow-lg p-8 lg:p-12">
            {activeTab === 'overview' && (
              <div className="grid lg:grid-cols-2 gap-12 items-center">
                <div className="space-y-6">
                  <h3 className="text-2xl font-bold text-gray-900">Why Choose Alensoft EnMS?</h3>
                  <p className="text-gray-600 leading-relaxed">
                    In today's competitive industrial landscape, optimizing energy consumption isn't just about reducing costs—it's about sustainability, compliance, and gaining a competitive edge. ALENSOFT EnMS delivers a comprehensive solution that transforms how you monitor, manage, and optimize resources across your entire operations.
                  </p>
                  <div className="space-y-4">
                    <div className="flex items-start space-x-3">
                      <CheckCircle className="w-6 h-6 text-green-600 mt-1 flex-shrink-0" />
                      <div>
                        <h4 className="font-semibold text-gray-900">Cost Optimization</h4>
                        <p className="text-gray-600">Reduce energy costs by 8-15% through intelligent monitoring and optimization.</p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3">
                      <CheckCircle className="w-6 h-6 text-green-600 mt-1 flex-shrink-0" />
                      <div>
                        <h4 className="font-semibold text-gray-900">Sustainability Goals</h4>
                        <p className="text-gray-600">Meet environmental targets with comprehensive carbon footprint management.</p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3">
                      <CheckCircle className="w-6 h-6 text-green-600 mt-1 flex-shrink-0" />
                      <div>
                        <h4 className="font-semibold text-gray-900">Compliance Ready</h4>
                        <p className="text-gray-600">Built-in support for ISO 50001 and international energy standards.</p>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="relative">
                  <img 
                    src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=600&h=400&fit=crop&auto=format" 
                    alt="Energy Management Overview"
                    className="rounded-xl shadow-lg"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-green-600/20 to-transparent rounded-xl"></div>
                </div>
              </div>
            )}

            {activeTab === 'features' && (
              <div className="space-y-8">
                <h3 className="text-2xl font-bold text-gray-900 text-center">Core Platform Features</h3>
                <div className="grid md:grid-cols-2 gap-6">
                  {coreFeatures.map((feature, index) => (
                    <div key={index} className="flex items-center space-x-3 p-4 bg-green-50 rounded-lg border border-green-100">
                      <CheckCircle className="w-6 h-6 text-green-600 flex-shrink-0" />
                      <span className="text-gray-800 font-medium">{feature}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {activeTab === 'benefits' && (
              <div className="space-y-8">
                <h3 className="text-2xl font-bold text-gray-900 text-center">Key Benefits</h3>
                <div className="grid md:grid-cols-2 gap-8">
                  {benefits.map((benefit, index) => {
                    const IconComponent = benefit.icon;
                    return (
                      <div key={index} className="flex items-start space-x-4 p-6 bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl border border-green-100">
                        <div className="bg-green-600 p-3 rounded-lg">
                          <IconComponent className="w-6 h-6 text-white" />
                        </div>
                        <div>
                          <h4 className="text-xl font-semibold text-gray-900 mb-2">{benefit.title}</h4>
                          <p className="text-gray-600">{benefit.description}</p>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}

            {activeTab === 'modules' && (
              <div className="space-y-8">
                <h3 className="text-2xl font-bold text-gray-900 text-center">Specialized Add-on Modules</h3>
                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {addOnModules.map((module, index) => {
                    const IconComponent = module.icon;
                    return (
                      <div 
                        key={index} 
                        className="bg-white border border-gray-200 rounded-xl p-6 hover:shadow-xl hover:border-green-300 transition-all cursor-pointer group"
                        onClick={() => setSelectedModule(selectedModule === index ? null : index)}
                      >
                        <div className="flex items-center space-x-3 mb-4">
                          <div className="bg-green-100 group-hover:bg-green-600 p-2 rounded-lg transition-colors">
                            <IconComponent className="w-6 h-6 text-green-600 group-hover:text-white transition-colors" />
                          </div>
                          <h4 className="font-semibold text-gray-900 group-hover:text-green-600 transition-colors">{module.title}</h4>
                        </div>
                        <p className="text-gray-600 mb-4">{module.description}</p>
                        
                        {selectedModule === index && (
                          <div className="space-y-2 pt-4 border-t border-gray-100">
                            {module.features.map((feature, featureIndex) => (
                              <div key={featureIndex} className="flex items-center space-x-2">
                                <CheckCircle className="w-4 h-4 text-green-600 flex-shrink-0" />
                                <span className="text-sm text-gray-700">{feature}</span>
                              </div>
                            ))}
                          </div>
                        )}
                        
                        <div className="flex items-center text-green-600 text-sm font-medium mt-4">
                          <span>{selectedModule === index ? 'Hide Details' : 'View Details'}</span>
                          <ArrowRight className={`w-4 h-4 ml-1 transition-transform ${selectedModule === index ? 'rotate-90' : ''}`} />
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="py-16 bg-gradient-to-r from-green-600 to-emerald-700">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl lg:text-4xl font-bold text-white mb-6">
            Ready to Transform Your Energy Management?
          </h2>
          <p className="text-xl text-green-100 mb-8 max-w-2xl mx-auto">
            Join 300+ satisfied customers who have achieved 8-15% energy savings with our intelligent EnMS platform.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button className="bg-white text-green-700 px-8 py-4 rounded-lg font-semibold hover:bg-green-50 transition-colors flex items-center justify-center">
              <Phone className="w-5 h-5 mr-2" />
              Request Consultation
            </button>
            <button className="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold hover:bg-white hover:text-green-700 transition-colors flex items-center justify-center">
              <Mail className="w-5 h-5 mr-2" />
              Get Quote
            </button>
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="space-y-4">
              <h3 className="text-xl font-bold text-green-400">ALENSOFT</h3>
              <p className="text-gray-400">Leading provider of intelligent energy management solutions for industrial operations.</p>
            </div>
            <div className="space-y-4">
              <h4 className="font-semibold">Product</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white transition-colors">Features</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Add-on Modules</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Integrations</a></li>
              </ul>
            </div>
            <div className="space-y-4">
              <h4 className="font-semibold">Support</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white transition-colors">Documentation</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Training</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Contact</a></li>
              </ul>
            </div>
            <div className="space-y-4">
              <h4 className="font-semibold">Company</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white transition-colors">About Us</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Case Studies</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Careers</a></li>
              </ul>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-12 pt-8 text-center text-gray-400">
            <p>&copy; 2025 Alensoft. All rights reserved. | Smart Energy Management System (EnMS)</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default AlensoftEnMSProductPage;